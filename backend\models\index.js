const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', typeof process.env.DB_PASSWORD, process.env.DB_PASSWORD ? '***' : '(empty)');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false
  }
);

const db = {};

fs.readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 &&
      file !== 'index.js' &&
      file.slice(-3) === '.js'
    );
  })
  .forEach(file => {
    try {
      console.log(`[Models] Loading model file: ${file}`);
      const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
      console.log(`[Models] Loaded model: ${model.name}`);
      db[model.name] = model;
    } catch (error) {
      console.error(`[Models] Error loading model ${file}:`, error);
    }
  });

db.AuditPlan = require('./Audit/auditplan')(sequelize, Sequelize.DataTypes);
db.AuditMission = require('./Audit/auditmission')(sequelize, Sequelize.DataTypes);
db.AuditActivity = require('./Audit/auditactivity')(sequelize, Sequelize.DataTypes);
db.AuditConstat = require('./Audit/auditconstat')(sequelize, Sequelize.DataTypes);
db.AuditScope = require('./Audit/auditscope')(sequelize, Sequelize.DataTypes);
db.AuditRecommendation = require('./Audit/auditrecommendation')(sequelize, Sequelize.DataTypes);
db.AuditMissionRapport = require('./Audit/rapport/auditmissionrapport')(sequelize);
db.AuditMissionRapportSupport = require('./Audit/rapport/auditmissionrapportsupport')(sequelize, Sequelize.DataTypes);
db.FicheDeTravail = require('./Audit/auditfichedetravail')(sequelize, Sequelize.DataTypes);
db.Question = require('./Audit/question')(sequelize, Sequelize.DataTypes);
db.FicheDeTest = require('./Audit/auditfichdetest')(sequelize, Sequelize.DataTypes);
db.FicheDeTestAttachment = require('./Audit/attachment/fichedetestAttachment')(sequelize, Sequelize);
db.AllAttachment = require('./AllAttachment')(sequelize, Sequelize.DataTypes);
db.FicheTestResponse = require('./Audit/fichetestresponse')(sequelize, Sequelize.DataTypes);
db.ControlQuestion = require('./ControlQuestion')(sequelize, Sequelize.DataTypes);
db.ControlQuestionAssignment = require('./ControlQuestionAssignment')(sequelize);
db.ControlMethodeExecution = require('./ControlMethodeExecution')(sequelize, Sequelize.DataTypes);
db.Campagne = require('./campagne')(sequelize, Sequelize.DataTypes);
db.CampagneResponse = require('./CampagneResponse')(sequelize, Sequelize.DataTypes);
db.MissionCharts = require('./rapport/MissionCharts');
db.EquipeIntervenante = require('./Audit/equipeintervenante')(sequelize, Sequelize.DataTypes);

// Audit Skills models
db.AuditSkill = require('./Audit/auditskill')(sequelize, Sequelize.DataTypes);
db.AuditorSkill = require('./Audit/auditorskill')(sequelize, Sequelize.DataTypes);
db.AuditMissionSkill = require('./Audit/audit-mission-skill')(sequelize, Sequelize.DataTypes);


const expectedModels = [
  'User', 'Incident', 'Risk', 'Control', 'Entity', 'RiskType', 'BusinessLine',
  'IncidentType', 'BusinessProcess', 'OrganizationalProcess', 'Product',
  'Application', 'Operation', 'ControlType', 'Process', 'ActionPlan', 'Attachment',
  'RiskAttachment', 'IncidentLoss', 'IncidentGain', 'IncidentRecovery', 'IncidentProvision',
  'Role', 'UserRole', 'Event', 'Notification', 'AuditPlan', 'AuditMission',
  'AuditActivity', 'AuditConstat', 'AuditScope', 'AuditRecommendation',
  'AuditMissionRapport', 'AuditMissionRapportSupport', 'FicheDeTravail', 'Question', 'AllAttachment', 'ControlQuestion', 'ControlQuestionAssignment', 'ControlMethodeExecution',
  'Campagne', 'CampagneResponse', 'ActivityLog', 'IncidentActivityLog', 'ControlActivityLog', 'Activity'
];
expectedModels.forEach(model => {
  if (!db[model] && process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
    console.warn(`Warning: Model ${model} is not loaded`);
  }
});

if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
  console.log('Loaded models:', Object.keys(db));
}

// Import AuditPlan model
db.AuditPlan = require('./Audit/auditplan')(sequelize, Sequelize.DataTypes);

// Add this line after the AuditPlan model import
db.AuditMission = require('./Audit/auditmission')(sequelize, Sequelize.DataTypes);

// Add this line after the AuditMission model import
db.AuditActivity = require('./Audit/auditactivity')(sequelize, Sequelize.DataTypes);

// Add this line after the AuditActivity model import
db.AuditConstat = require('./Audit/auditconstat')(sequelize, Sequelize.DataTypes);

// Add this line after the AuditConstat model import
db.AuditScope = require('./Audit/auditscope')(sequelize, Sequelize.DataTypes);

// Add this line for AuditRecommendation
db.AuditRecommendation = require('./Audit/auditrecommendation')(sequelize, Sequelize.DataTypes);

// Add this line for AuditMissionRapport
db.AuditMissionRapport = require('./Audit/rapport/auditmissionrapport')(sequelize);

// Set up associations dynamically if models have an `associate` method
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

// --- Automatic Role Seeding ---
async function seedDefaultRoles() {
  const defaultRoles = [
    { name: 'GRC Administrator', code: 'grc_admin', description: 'Full access to all GRC features and administrative functions' },
    { name: 'GRC Manager', code: 'grc_manager', description: 'Manages GRC processes and has access to most features' },
    { name: 'Risk Manager', code: 'risk_manager', description: 'Manages risks and related processes' },
    { name: 'GRC Contributor', code: 'grc_contributor', description: 'Can contribute to GRC processes with limited access' },
    { name: 'Incident Manager', code: 'incident_manager', description: 'Manages incidents, incident response, and related processes' },
    { name: 'Internal Controller', code: 'internal_controller', description: 'Responsible for internal control design, implementation, and monitoring' },
    { name: 'Compliance Manager', code: 'compliance_manager', description: 'Manages compliance programs, policies, and regulatory requirements' },
    { name: 'Audit Director', code: 'audit_director', description: 'Directs internal/external audit activities and oversees audit teams' },
    { name: 'Auditor', code: 'auditor', description: 'Conducts audits, tests controls, and reports findings' }
  ];
  if (db.Role) {
    for (const role of defaultRoles) {
      await db.Role.findOrCreate({ where: { code: role.code }, defaults: role });
    }
    console.log('[Seed] Default roles ensured in Roles table');
  } else {
    console.warn('[Seed] Role model not found, skipping role seeding');
  }
}

// Call the seeding function immediately (but after associations)
seedDefaultRoles().catch(err => {
  console.error('[Seed] Error seeding default roles:', err);
});

// Specific associations for Incident
if (db.Incident) {
  if (db.Entity) {
    db.Incident.belongsTo(db.Entity, { foreignKey: 'entityID', as: 'entity' });
    db.Entity.hasMany(db.Incident, { foreignKey: 'entityID', as: 'incidents' });
  }
  if (db.Risk) {
    db.Incident.belongsTo(db.Risk, { foreignKey: 'riskID', as: 'risk' });
    db.Risk.hasMany(db.Incident, { foreignKey: 'riskID', as: 'incidents' });
  }
  if (db.Control) {
    db.Incident.belongsTo(db.Control, { foreignKey: 'controlID', as: 'control' });
    db.Control.hasMany(db.Incident, { foreignKey: 'controlID', as: 'incidents' });
  }
  if (db.BusinessLine) {
    db.Incident.belongsTo(db.BusinessLine, { foreignKey: 'businessLineID', as: 'businessLine' });
    db.BusinessLine.hasMany(db.Incident, { foreignKey: 'businessLineID', as: 'incidents' });
  }
  if (db.IncidentType) {
    db.Incident.belongsTo(db.IncidentType, { foreignKey: 'incidentTypeID', as: 'incidentType' });
    db.IncidentType.hasMany(db.Incident, { foreignKey: 'incidentTypeID', as: 'incidents' });
  }
  if (db.BusinessProcess) {
    db.Incident.belongsTo(db.BusinessProcess, { foreignKey: 'businessProcessID', as: 'businessProcess' });
    db.BusinessProcess.hasMany(db.Incident, { foreignKey: 'businessProcessID', as: 'incidents' });
  }
  if (db.OrganizationalProcess) {
    db.Incident.belongsTo(db.OrganizationalProcess, { foreignKey: 'organizationalProcessID', as: 'organizationalProcess' });
    db.OrganizationalProcess.hasMany(db.Incident, { foreignKey: 'organizationalProcessID', as: 'incidents' });
  }
  if (db.Product) {
    db.Incident.belongsTo(db.Product, { foreignKey: 'productID', as: 'product' });
    db.Product.hasMany(db.Incident, { foreignKey: 'productID', as: 'incidents' });
  }
  if (db.Application) {
    db.Incident.belongsTo(db.Application, { foreignKey: 'applicationID', as: 'application' });
    db.Application.hasMany(db.Incident, { foreignKey: 'applicationID', as: 'incidents' });
  }
}

// Associations for Control
if (db.Control) {
  if (db.BusinessProcess) {
    db.Control.belongsTo(db.BusinessProcess, { foreignKey: 'businessProcess', as: 'businessProcessRef', targetKey: 'businessProcessID' });
    db.BusinessProcess.hasMany(db.Control, { foreignKey: 'businessProcess', as: 'controls', sourceKey: 'businessProcessID' });
  }
  if (db.OrganizationalProcess) {
    db.Control.belongsTo(db.OrganizationalProcess, { foreignKey: 'organizationalProcess', as: 'organizationalProcessRef', targetKey: 'organizationalProcessID' });
    db.OrganizationalProcess.hasMany(db.Control, { foreignKey: 'organizationalProcess', as: 'controls', sourceKey: 'organizationalProcessID' });
  }
  if (db.Operation) {
    db.Control.belongsTo(db.Operation, { foreignKey: 'operation', as: 'operationRef', targetKey: 'operationID' });
    db.Operation.hasMany(db.Control, { foreignKey: 'operation', as: 'controls', sourceKey: 'operationID' });
  }
  if (db.Application) {
    db.Control.belongsTo(db.Application, { foreignKey: 'application', as: 'applicationRef', targetKey: 'applicationID' });
    db.Application.hasMany(db.Control, { foreignKey: 'application', as: 'controls', sourceKey: 'applicationID' });
  }
  if (db.Entity) {
    db.Control.belongsTo(db.Entity, { foreignKey: 'entity', as: 'entityRef', targetKey: 'entityID' });
    db.Entity.hasMany(db.Control, { foreignKey: 'entity', as: 'controls', sourceKey: 'entityID' });
  }
  if (db.ControlType) {
    db.Control.belongsTo(db.ControlType, { foreignKey: 'controlType', as: 'controlTypeRef', targetKey: 'controlTypeID' });
    db.ControlType.hasMany(db.Control, { foreignKey: 'controlType', as: 'controls', sourceKey: 'controlTypeID' });
  }
  if (db.Risk) {
    db.Control.belongsTo(db.Risk, { foreignKey: 'risk', as: 'riskRef', targetKey: 'riskID' });
    db.Risk.hasMany(db.Control, { foreignKey: 'risk', as: 'controls', sourceKey: 'riskID' });
  }
}

// Self-referential associations for Entity
if (db.Entity) {
  db.Entity.belongsTo(db.Entity, { foreignKey: 'parentEntityID', as: 'parentEntity' });
  db.Entity.hasMany(db.Entity, { foreignKey: 'parentEntityID', as: 'childEntities' });
}

// Self-referential associations for ControlType
if (db.ControlType) {
  db.ControlType.belongsTo(db.ControlType, { foreignKey: 'parentControlTypeID', as: 'parentControlType' });
  db.ControlType.hasMany(db.ControlType, { foreignKey: 'parentControlTypeID', as: 'childControlTypes' });
}

// Self-referential associations for BusinessProcess
if (db.BusinessProcess) {
  db.BusinessProcess.belongsTo(db.BusinessProcess, {
    foreignKey: 'parentBusinessProcessID',
    as: 'parentBusinessProcess'
  });
  db.BusinessProcess.hasMany(db.BusinessProcess, {
    foreignKey: 'parentBusinessProcessID',
    as: 'childBusinessProcesses'
  });
}

// Associations for Operation
if (db.Operation) {
  if (db.Entity) {
    db.Operation.belongsTo(db.Entity, { foreignKey: 'entityID', as: 'entity' });
    db.Entity.hasMany(db.Operation, { foreignKey: 'entityID', as: 'operations' });
  }
  if (db.Incident) {
    db.Operation.belongsTo(db.Incident, { foreignKey: 'incidentID', as: 'incident' });
    db.Incident.hasMany(db.Operation, { foreignKey: 'incidentID', as: 'operations' });
  }
  if (db.OrganizationalProcess) {
    db.Operation.belongsTo(db.OrganizationalProcess, {
      foreignKey: 'parentOrganizationalProcess',
      as: 'parentOrgProcess',
      targetKey: 'organizationalProcessID'
    });
    db.OrganizationalProcess.hasMany(db.Operation, {
      foreignKey: 'parentOrganizationalProcess',
      as: 'operations',
      sourceKey: 'organizationalProcessID'
    });
  }
}

// Associations for Process
if (db.Process) {
  if (db.Entity) {
    db.Process.belongsTo(db.Entity, { foreignKey: 'entityID', as: 'entity' });
    db.Entity.hasMany(db.Process, { foreignKey: 'entityID', as: 'processes' });
  }
  if (db.Operation) {
    db.Process.belongsTo(db.Operation, { foreignKey: 'operationID', as: 'operation' });
    db.Operation.hasMany(db.Process, { foreignKey: 'operationID', as: 'processes' });
  }
}

// Additional associations for Risk
if (db.Risk) {
  if (db.BusinessProcess) {
    db.Risk.belongsTo(db.BusinessProcess, { foreignKey: 'businessProcessID', as: 'businessProcess' });
    db.BusinessProcess.hasMany(db.Risk, { foreignKey: 'businessProcessID', as: 'risks' });
  }
  if (db.OrganizationalProcess) {
    db.Risk.belongsTo(db.OrganizationalProcess, { foreignKey: 'organizationalProcessID', as: 'organizationalProcess' });
    db.OrganizationalProcess.hasMany(db.Risk, { foreignKey: 'organizationalProcessID', as: 'risks' });
  }
  if (db.Operation) {
    db.Risk.belongsTo(db.Operation, { foreignKey: 'operationID', as: 'operation' });
    db.Operation.hasMany(db.Risk, { foreignKey: 'operationID', as: 'risks' });
  }
  if (db.Application) {
    db.Risk.belongsTo(db.Application, { foreignKey: 'applicationID', as: 'application' });
    db.Application.hasMany(db.Risk, { foreignKey: 'applicationID', as: 'risks' });
  }
  if (db.Entity) {
    db.Risk.belongsTo(db.Entity, { foreignKey: 'entityID', as: 'entity' });
    db.Entity.hasMany(db.Risk, { foreignKey: 'entityID', as: 'risks' });
  }
  if (db.RiskType) {
    db.Risk.belongsTo(db.RiskType, { foreignKey: 'riskTypeID', as: 'riskType' });
    db.RiskType.hasMany(db.Risk, { foreignKey: 'riskTypeID', as: 'risks' });
  }
  if (db.Control) {
    db.Risk.belongsTo(db.Control, { foreignKey: 'controlID', as: 'control' });
    db.Control.hasMany(db.Risk, { foreignKey: 'controlID', as: 'risks' });
  }
}

// Add associations for RiskAttachment
if (db.RiskAttachment) {
  if (db.Risk) {
    db.Risk.hasMany(db.RiskAttachment, { foreignKey: 'riskID', as: 'attachments' });
  }
}

// Export sequelize and Sequelize
db.sequelize = sequelize;
db.Sequelize = Sequelize;

// Sync database with { alter: true } to create the RiskAttachment table
// Commented out since we're using migrations instead

// sequelize.sync({ alter: false })
//   .then(() => {
//     if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
//       console.log('Database synced with models');
//     }
//   })
//   .catch(err => {
//     console.error('Error syncing database:', err);
//   });

module.exports = db;
