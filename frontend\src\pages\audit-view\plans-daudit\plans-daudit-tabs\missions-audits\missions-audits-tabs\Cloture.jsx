import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  FileCheck,
  ChevronDown,
  ChevronUp,
  CheckSquare,
  FileText,
  FileDown,
  Loader2,
  AlertTriangle,
  RefreshCw,
  Mail,
  Eye,
  Volume2,
  Pause,
  Play,
  Square,
  MessageCircle,
  Upload,
  Image,
  User,
  PenTool
} from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import EmailReportModal from '@/components/reports/EmailReportModal';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { useSelector, useDispatch } from 'react-redux';
import { updateAuditMission } from '@/store/slices/audit/auditMissionsSlice';
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";

function ConclusionTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const API_BASE_URL = getApiBaseUrl();
  const dispatch = useDispatch();

  // Section states
  const [isConclusionOpen, setIsConclusionOpen] = useState(true);
  const [isRapportOpen, setIsRapportOpen] = useState(true);

  // Conclusion section state (from caracteristiques)
  const [characteristics, setCharacteristics] = useState({
    pointsForts: '',
    pointsFaibles: '',
    evaluationLevel: ''
  });
  const [isSavingConclusion, setIsSavingConclusion] = useState(false);

  // Report functionality state (from rapport tab)
  const [reportData, setReportData] = useState(null);
  const [loadingReport, setLoadingReport] = useState(false);
  const [downloadingPdf, setDownloadingPdf] = useState(false);
  const [downloadingDocx, setDownloadingDocx] = useState(false);
  const [reportError, setReportError] = useState(null);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [emailAttachment, setEmailAttachment] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(false);
  const [pdfError, setPdfError] = useState(null);

  // Rapport Support state
  const [rapportSupport, setRapportSupport] = useState({
    logo: '',
    destinataire: '',
    signatureElectrique: ''
  });
  const [loadingRapportSupport, setLoadingRapportSupport] = useState(false);
  const [savingRapportSupport, setSavingRapportSupport] = useState(false);

  // TTS state
  const [ttsLoading, setTtsLoading] = useState(false);
  const [ttsError, setTtsError] = useState(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [spokenText, setSpokenText] = useState('');
  const speechSynthesisRef = React.useRef(window.speechSynthesis);
  const utteranceRef = React.useRef(null);

  // Gemini conversation state
  const [conversation, setConversation] = useState([]);
  const [convLoading, setConvLoading] = useState(false);
  const [convError, setConvError] = useState(null);
  const [isConvPlaying, setIsConvPlaying] = useState(false);
  const [isConvPaused, setIsConvPaused] = useState(false);
  const [convIndex, setConvIndex] = useState(0);
  const stopReadingRef = React.useRef(false);

  // Helper functions for conclusion section
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCharacteristics(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setCharacteristics(prev => ({ ...prev, [name]: value }));
  };

  // Save conclusion data (silent auto-save)
  const saveConclusion = async (showToast = false) => {
    if (!missionAudit?.id) return;

    setIsSavingConclusion(true);
    try {
      const updateData = {
        pointfort: characteristics.pointsForts,
        pointfaible: characteristics.pointsFaibles,
        evaluation: characteristics.evaluationLevel,
      };

      await dispatch(updateAuditMission({ id: missionAudit.id, missionData: updateData })).unwrap();
      if (showToast) {
        toast.success('Conclusion sauvegardée avec succès');
      }
    } catch (error) {
      console.error('Error saving conclusion:', error);
      if (error && error.name !== 'CanceledError') {
        toast.error('Erreur lors de la sauvegarde de la conclusion');
      }
    } finally {
      setIsSavingConclusion(false);
    }
  };

  const getEvaluationColorClass = (level) => {
    switch (level) {
      case 'Bon niveau': return 'bg-green-500';
      case 'Peut être améliorée': return 'bg-yellow-500';
      case 'Amélioration nécessaire': return 'bg-orange-500';
      case 'A risque': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  // Helper functions for rapport support
  const handleRapportSupportChange = (field, value) => {
    console.log(`Updating ${field}:`, value ? 'Present' : 'Empty');
    setRapportSupport(prev => {
      const newState = { ...prev, [field]: value };
      console.log('New rapport support state:', {
        logo: newState.logo ? 'Present' : 'Empty',
        destinataire: newState.destinataire || 'Empty',
        signatureElectrique: newState.signatureElectrique ? 'Present' : 'Empty'
      });
      return newState;
    });
  };

  const handleFileUpload = (field, event) => {
    const file = event.target.files[0];
    if (file) {
      console.log(`Uploading ${field}:`, file.name, file.type, file.size);

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Veuillez sélectionner un fichier image valide');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('La taille du fichier ne doit pas dépasser 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target.result;
        console.log(`${field} base64 length:`, base64.length);
        handleRapportSupportChange(field, base64);
        toast.success(`${field === 'logo' ? 'Logo' : 'Signature'} téléchargé avec succès`);
      };
      reader.onerror = () => {
        toast.error('Erreur lors de la lecture du fichier');
      };
      reader.readAsDataURL(file);
    }
  };

  // Fetch rapport support data
  const fetchRapportSupport = useCallback(async () => {
    if (!missionAudit?.id) return;

    try {
      setLoadingRapportSupport(true);
      console.log('Fetching rapport support for mission:', missionAudit.id);

      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapport-support/mission/${missionAudit.id}`,
        { withCredentials: true }
      );

      console.log('Rapport support response:', response.data);

      if (response.data.success && response.data.data) {
        const data = response.data.data;
        console.log('Setting rapport support data:', data);
        setRapportSupport({
          logo: data.logo || '',
          destinataire: data.destinataire || '',
          signatureElectrique: data.signatureElectrique || ''
        });
      }
    } catch (error) {
      if (error.response?.status !== 404) {
        console.error('Error fetching rapport support:', error);
        toast.error('Erreur lors du chargement des données de support du rapport');
      } else {
        console.log('No rapport support found for mission:', missionAudit.id);
        // Initialize with empty values if no data exists
        setRapportSupport({
          logo: '',
          destinataire: '',
          signatureElectrique: ''
        });
      }
    } finally {
      setLoadingRapportSupport(false);
    }
  }, [missionAudit?.id, API_BASE_URL]);

  // Save rapport support data
  const saveRapportSupport = async () => {
    if (!missionAudit?.id) return;

    try {
      setSavingRapportSupport(true);
      console.log('Saving rapport support data:', rapportSupport);

      // Check if rapport support exists
      let existingResponse;
      try {
        existingResponse = await axios.get(
          `${API_BASE_URL}/audit-mission-rapport-support/mission/${missionAudit.id}`,
          { withCredentials: true }
        );
        console.log('Existing rapport support found:', existingResponse.data);
      } catch (error) {
        if (error.response?.status !== 404) {
          throw error;
        }
        console.log('No existing rapport support found, will create new');
      }

      const rapportSupportData = {
        logo: rapportSupport.logo,
        destinataire: rapportSupport.destinataire,
        signatureElectrique: rapportSupport.signatureElectrique,
        auditMissionId: missionAudit.id
      };

      let saveResponse;
      if (existingResponse?.data?.success) {
        // Update existing
        console.log('Updating existing rapport support');
        saveResponse = await axios.put(
          `${API_BASE_URL}/audit-mission-rapport-support/${existingResponse.data.data.id}`,
          rapportSupportData,
          { withCredentials: true }
        );
      } else {
        // Create new
        console.log('Creating new rapport support');
        saveResponse = await axios.post(
          `${API_BASE_URL}/audit-mission-rapport-support`,
          rapportSupportData,
          { withCredentials: true }
        );
      }

      console.log('Save response:', saveResponse.data);
      toast.success('Configuration du rapport sauvegardée avec succès');
    } catch (error) {
      console.error('Error saving rapport support:', error);
      toast.error('Erreur lors de la sauvegarde de la configuration du rapport');
    } finally {
      setSavingRapportSupport(false);
    }
  };

  // Helper functions for report section (from rapport tab)
  const getVoice = (gender) => {
    const voices = window.speechSynthesis.getVoices();
    if (gender === 'female') {
      return voices.find(v => v.name === 'Microsoft Julie') ||
             voices.find(v => v.name === 'Microsoft Hortense') ||
             voices.find(v => v.name === 'Google français') ||
             voices.find(v => v.lang === 'fr-FR');
    } else {
      return voices.find(v => v.name === 'Microsoft Paul') ||
             voices.find(v => v.lang === 'fr-FR' && v.name.toLowerCase().includes('paul')) ||
             voices.find(v => v.lang === 'fr-FR');
    }
  };

  const getConversationText = (conv) => {
    return conv.map(msg => `${msg.speaker}: ${msg.text}`).join('\n');
  };

  const readConversationAloud = async (conv, startIdx = 0) => {
    if (!window.speechSynthesis) return;
    stopReadingRef.current = false;
    setIsConvPlaying(true);
    setIsConvPaused(false);
    for (let i = startIdx; i < conv.length; i++) {
      if (stopReadingRef.current) break;
      setConvIndex(i);
      const msg = conv[i];
      const utterance = new window.SpeechSynthesisUtterance(msg.text);
      utterance.lang = 'fr-FR';
      if (msg.speaker && msg.speaker.toLowerCase() === 'alice') {
        utterance.voice = getVoice('female');
      } else if (msg.speaker && msg.speaker.toLowerCase() === 'bob') {
        utterance.voice = getVoice('male');
      }
      await new Promise((resolve) => {
        utterance.onend = resolve;
        utterance.onerror = resolve;
        window.speechSynthesis.speak(utterance);
      });
      while (isConvPaused && !stopReadingRef.current) {
        await new Promise(r => setTimeout(r, 100));
      }
    }
    setIsConvPlaying(false);
    setIsConvPaused(false);
    setConvIndex(0);
  };

  // Fetch report data for preview
  const fetchReportData = useCallback(async () => {
    if (!missionAudit?.id) return;

    try {
      setLoadingReport(true);
      setReportError(null);

      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}`,
        { withCredentials: true }
      );

      // Check if response contains enhanced data with rapport support
      if (response.data.reportData) {
        // Enhanced response format
        setReportData([response.data.reportData]);
        if (response.data.rapportSupport) {
          setRapportSupport({
            logo: response.data.rapportSupport.logo || '',
            destinataire: response.data.rapportSupport.destinataire || '',
            signatureElectrique: response.data.rapportSupport.signatureElectrique || ''
          });
        }
      } else {
        // Original response format
        setReportData(response.data);
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      setReportError('Erreur lors du chargement des données du rapport');
      toast.error('Erreur lors du chargement des données du rapport');
    } finally {
      setLoadingReport(false);
    }
  }, [missionAudit?.id, API_BASE_URL]);

  // Fetch conclusion data when component mounts or mission changes
  useEffect(() => {
    if (missionAudit?.id) {
      setCharacteristics({
        pointsForts: missionAudit.pointfort || '',
        pointsFaibles: missionAudit.pointfaible || '',
        evaluationLevel: missionAudit.evaluation || 'Bon niveau'
      });
    }
  }, [missionAudit]);

  // Auto-save conclusion data when characteristics change (debounced)
  useEffect(() => {
    if (!missionAudit?.id) return;

    // Don't auto-save on initial load
    const hasInitialData = missionAudit.pointfort || missionAudit.pointfaible || missionAudit.evaluation;
    if (!hasInitialData && !characteristics.pointsForts && !characteristics.pointsFaibles && characteristics.evaluationLevel === 'Bon niveau') {
      return;
    }

    const timeoutId = setTimeout(() => {
      saveConclusion();
    }, 1000); // Auto-save after 1 second of inactivity

    return () => clearTimeout(timeoutId);
  }, [characteristics.pointsForts, characteristics.pointsFaibles, characteristics.evaluationLevel]);

  // Fetch report data when component mounts or mission changes
  useEffect(() => {
    if (missionAudit?.id) {
      console.log('useEffect triggered for mission:', missionAudit.id);
      fetchReportData();
      fetchRapportSupport();
    }
  }, [missionAudit?.id, fetchReportData, fetchRapportSupport]);

  // Debug effect to log state changes
  useEffect(() => {
    console.log('Rapport support state changed:', {
      logo: rapportSupport.logo ? 'Present' : 'Empty',
      destinataire: rapportSupport.destinataire || 'Empty',
      signatureElectrique: rapportSupport.signatureElectrique ? 'Present' : 'Empty'
    });
  }, [rapportSupport]);

  // Auto-save rapport support data when it changes (debounced)
  useEffect(() => {
    if (!missionAudit?.id || loadingRapportSupport) return;

    // Don't auto-save on initial load - check if we have any meaningful data
    const hasData = rapportSupport.logo || rapportSupport.destinataire || rapportSupport.signatureElectrique;
    if (!hasData) {
      return;
    }

    console.log('Auto-saving rapport support data:', rapportSupport);

    const timeoutId = setTimeout(() => {
      saveRapportSupport();
    }, 1000); // Auto-save after 1 second of inactivity

    return () => clearTimeout(timeoutId);
  }, [rapportSupport.logo, rapportSupport.destinataire, rapportSupport.signatureElectrique, loadingRapportSupport]);

  // Download PDF report
  const downloadPdfReport = async () => {
    try {
      setDownloadingPdf(true);

      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}?format=pdf`,
        {
          withCredentials: true,
          responseType: 'blob'
        }
      );

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `rapport_audit_${missionAudit.id}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success('Rapport PDF téléchargé avec succès');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error('Erreur lors du téléchargement du PDF');
    } finally {
      setDownloadingPdf(false);
    }
  };

  // Download DOCX report
  const downloadDocxReport = async () => {
    try {
      setDownloadingDocx(true);

      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}?format=docx`,
        {
          withCredentials: true,
          responseType: 'blob'
        }
      );

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `rapport_audit_${missionAudit.id}.docx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success('Rapport DOCX téléchargé avec succès');
    } catch (error) {
      console.error('Error downloading DOCX:', error);
      toast.error('Erreur lors du téléchargement du DOCX');
    } finally {
      setDownloadingDocx(false);
    }
  };

  const openEmailModalWithPDF = async () => {
    try {
      setDownloadingPdf(true);
      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}?format=pdf`,
        {
          withCredentials: true,
          responseType: 'blob'
        }
      );
      const pdfBlob = response.data;
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result.split(',')[1];
        setEmailAttachment({
          base64,
          filename: `rapport_audit_${missionAudit.id}.pdf`,
          contentType: 'application/pdf',
        });
        setIsEmailModalOpen(true);
      };
      reader.readAsDataURL(pdfBlob);
    } catch (error) {
      toast.error('Erreur lors de la génération du PDF pour l\'email.');
    } finally {
      setDownloadingPdf(false);
    }
  };

  const handleOpenPreview = () => {
    setPdfError(null);
    setPdfLoading(true);
    setIsPreviewOpen(true);
  };

  const handlePdfLoad = () => {
    setPdfLoading(false);
  };

  const handlePdfError = () => {
    setPdfLoading(false);
    setPdfError('Erreur lors du chargement du PDF.');
  };

  // TTS handlers
  const handleReadAloud = async () => {
    setTtsError(null);
    setTtsLoading(true);
    setSpokenText('');
    try {
      const response = await axios.get(`${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}/pdf-text`, { withCredentials: true });
      const text = response.data.text;
      setSpokenText(text);
      if (!text || !window.speechSynthesis) {
        setTtsError('Impossible de lire le texte extrait.');
        setTtsLoading(false);
        return;
      }
      window.speechSynthesis.cancel();
      const utterance = new window.SpeechSynthesisUtterance(text);
      utterance.lang = 'fr-FR';
      utterance.onend = () => {
        setIsSpeaking(false);
        setIsPaused(false);
      };
      utterance.onerror = (e) => {
        setTtsError('Erreur lors de la lecture vocale.');
        setIsSpeaking(false);
        setIsPaused(false);
      };
      utteranceRef.current = utterance;
      window.speechSynthesis.speak(utterance);
      setIsSpeaking(true);
      setIsPaused(false);
    } catch (err) {
      setTtsError('Erreur lors de l\'extraction du texte du PDF.');
    } finally {
      setTtsLoading(false);
    }
  };

  const handlePauseTTS = () => {
    if (window.speechSynthesis.speaking && !window.speechSynthesis.paused) {
      window.speechSynthesis.pause();
      setIsPaused(true);
    }
  };

  const handleResumeTTS = () => {
    if (window.speechSynthesis.paused) {
      window.speechSynthesis.resume();
      setIsPaused(false);
    }
  };

  const handleStopTTS = () => {
    window.speechSynthesis.cancel();
    setIsSpeaking(false);
    setIsPaused(false);
  };

  const handleGenerateConversation = async () => {
    setConvError(null);
    setConvLoading(true);
    setConversation([]);
    stopReadingRef.current = false;
    setIsConvPlaying(false);
    setIsConvPaused(false);
    setConvIndex(0);
    try {
      const response = await axios.get(`${API_BASE_URL}/gemini/conversation/${missionAudit.id}`, { withCredentials: true });
      const conv = response.data.conversation || [];
      setConversation(conv);
      if (conv.length > 0) {
        readConversationAloud(conv);
      }
    } catch (err) {
      setConvError("Erreur lors de la génération de la conversation.");
    } finally {
      setConvLoading(false);
    }
  };

  const handlePlayPauseConv = () => {
    if (!isConvPlaying && conversation.length > 0) {
      if (isConvPaused) {
        setIsConvPaused(false);
        window.speechSynthesis.resume();
      } else {
        readConversationAloud(conversation, convIndex);
      }
    } else if (isConvPlaying && !isConvPaused) {
      setIsConvPaused(true);
      window.speechSynthesis.pause();
    }
  };

  const handleStopConvTTS = () => {
    stopReadingRef.current = true;
    window.speechSynthesis.cancel();
    setIsConvPlaying(false);
    setIsConvPaused(false);
    setConvIndex(0);
  };

  // Cleanup effects
  React.useEffect(() => {
    return () => {
      window.speechSynthesis.cancel();
    };
  }, []);

  React.useEffect(() => {
    return () => {
      stopReadingRef.current = true;
      window.speechSynthesis.cancel();
    };
  }, []);

  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement de la conclusion...</p>
      </div>
    );
  }

  // Debug render
  console.log('Rendering Cloture component with rapport support:', {
    logo: rapportSupport.logo ? 'Present' : 'Empty',
    destinataire: rapportSupport.destinataire || 'Empty',
    signatureElectrique: rapportSupport.signatureElectrique ? 'Present' : 'Empty',
    loading: loadingRapportSupport
  });

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <FileCheck className="h-6 w-6 mr-3 text-[#F62D51]" />
          Clôture
        </h2>
      </div>

      {/* Section Conclusion */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-cyan-50 to-blue-50 rounded-t-lg"
          onClick={() => setIsConclusionOpen(!isConclusionOpen)}
        >
          <div className="flex items-center gap-2">
            {isConclusionOpen ? (
              <ChevronUp className="h-5 w-5 text-cyan-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-cyan-600" />
            )}
            <CheckSquare className="h-5 w-5 text-cyan-600 mr-1" />
            <span className="text-lg font-medium text-cyan-800">Conclusion</span>
            {isSavingConclusion && (
              <div className="ml-auto flex items-center text-cyan-600">
                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                <span className="text-sm">Sauvegarde...</span>
              </div>
            )}
          </div>
        </button>

        {isConclusionOpen && (
          <div className="p-5 bg-white">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="pointsForts">Points forts clés</Label>
                <Textarea
                  id="pointsForts"
                  name="pointsForts"
                  value={characteristics.pointsForts}
                  onChange={handleInputChange}
                  placeholder="Points forts identifiés lors de l'audit"
                  rows={4}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="pointsFaibles">Points faibles clés</Label>
                <Textarea
                  id="pointsFaibles"
                  name="pointsFaibles"
                  value={characteristics.pointsFaibles}
                  onChange={handleInputChange}
                  placeholder="Points faibles identifiés lors de l'audit"
                  rows={4}
                  className="w-full"
                />
              </div>

              <div className="space-y-3">
                <Label>Évaluation globale</Label>
                <Select
                  name="evaluationLevel"
                  value={characteristics.evaluationLevel}
                  onValueChange={(value) => handleSelectChange("evaluationLevel", value)}
                  className="w-full"
                >
                  <SelectTrigger id="evaluationLevel" className="w-full">
                    <SelectValue placeholder="Sélectionner un niveau d'évaluation">
                      {characteristics.evaluationLevel && (
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-2 ${getEvaluationColorClass(characteristics.evaluationLevel)}`} />
                          {characteristics.evaluationLevel}
                        </div>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Bon niveau">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getEvaluationColorClass('Bon niveau')}`} />
                      Bon niveau
                    </SelectItem>
                    <SelectItem value="Peut être améliorée">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getEvaluationColorClass('Peut être améliorée')}`} />
                      Peut être améliorée
                    </SelectItem>
                    <SelectItem value="Amélioration nécessaire">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getEvaluationColorClass('Amélioration nécessaire')}`} />
                      Amélioration nécessaire
                    </SelectItem>
                    <SelectItem value="A risque">
                      <div className={`w-3 h-3 rounded-full mr-2 ${getEvaluationColorClass('A risque')}`} />
                      À risque
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>


            </div>
          </div>
        )}
      </div>

      {/* Section Rapport */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg"
          onClick={() => setIsRapportOpen(!isRapportOpen)}
        >
          <div className="flex items-center gap-2">
            {isRapportOpen ? (
              <ChevronUp className="h-5 w-5 text-green-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-green-600" />
            )}
            <FileText className="h-5 w-5 text-green-600 mr-1" />
            <span className="text-lg font-medium text-green-800">Rapport</span>
          </div>
        </button>

        {isRapportOpen && (
          <div className="p-5 bg-white">
            <div className="space-y-6">
              {/* Title and Action Buttons */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-[#1A202C] flex items-center">
                  <FileText className="h-6 w-6 mr-3 text-[#F62D51]" />
                  Rapport d'Audit - {missionAudit.name}
                </h3>
                <div className="flex justify-end space-x-3 w-full mt-2">
                  <Button
                    variant="default"
                    size="lg"
                    onClick={fetchReportData}
                    disabled={loadingReport}
                  >
                    {loadingReport ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4 mr-2" />
                    )}
                    Actualiser
                  </Button>
                  <Button
                    variant="default"
                    size="lg"
                    onClick={handleOpenPreview}
                    disabled={!reportData}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Prévisualiser
                  </Button>
                  <Button
                    variant="default"
                    size="lg"
                    onClick={openEmailModalWithPDF}
                    disabled={downloadingPdf || !reportData}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Envoyer Email
                  </Button>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="lg">
                        <FileDown className="h-4 w-4 mr-2" />
                        Exporter <ChevronDown className="h-4 w-4 ml-1" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-44 p-0">
                      <div className="flex flex-col">
                        <Button
                          variant="ghost"
                          className="justify-start w-full rounded-none"
                          onClick={downloadPdfReport}
                          disabled={downloadingPdf || !reportData}
                        >
                          {downloadingPdf ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <FileDown className="h-4 w-4 mr-2" />
                          )}
                          PDF
                        </Button>
                        <Button
                          variant="ghost"
                          className="justify-start w-full rounded-none"
                          onClick={downloadDocxReport}
                          disabled={downloadingDocx || !reportData}
                        >
                          {downloadingDocx ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <FileDown className="h-4 w-4 mr-2" />
                          )}
                          DOCX
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Rapport Support Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-[#F62D51]" />
                    Configuration du Rapport
                    <div className="ml-auto flex items-center space-x-2">
                      {savingRapportSupport && (
                        <div className="flex items-center text-blue-600">
                          <Loader2 className="h-4 w-4 animate-spin mr-1" />
                          <span className="text-sm">Sauvegarde...</span>
                        </div>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchRapportSupport}
                        disabled={loadingRapportSupport}
                      >
                        {loadingRapportSupport ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-1" />
                        ) : (
                          <RefreshCw className="h-4 w-4 mr-1" />
                        )}
                        Actualiser
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={saveRapportSupport}
                        disabled={savingRapportSupport}
                      >
                        {savingRapportSupport ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-1" />
                        ) : (
                          <Upload className="h-4 w-4 mr-1" />
                        )}
                        Sauvegarder
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {loadingRapportSupport ? (
                    <div className="flex items-center justify-center h-32">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      <span className="ml-2">Chargement de la configuration...</span>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Logo Upload */}
                      <div className="space-y-2">
                        <Label htmlFor="logo-upload" className="flex items-center">
                          <Image className="h-4 w-4 mr-2 text-blue-600" />
                          Logo
                        </Label>
                        <div className="space-y-2">
                          <Input
                            id="logo-upload"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileUpload('logo', e)}
                            className="w-full"
                          />
                          {rapportSupport.logo && (
                            <div className="mt-2">
                              <img
                                src={rapportSupport.logo}
                                alt="Logo preview"
                                className="max-w-full h-20 object-contain border rounded"
                                onError={(e) => {
                                  console.error('Error loading logo preview');
                                  e.target.style.display = 'none';
                                }}
                              />
                            </div>
                          )}
                          <div className="text-xs text-gray-400 mt-1">
                            Status: {rapportSupport.logo ? 'Logo chargé' : 'Aucun logo'}
                          </div>
                        </div>
                      </div>

                      {/* Destinataire */}
                      <div className="space-y-2">
                        <Label htmlFor="destinataire" className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-green-600" />
                          Destinataire
                        </Label>
                        <Textarea
                          id="destinataire"
                          value={rapportSupport.destinataire}
                          onChange={(e) => handleRapportSupportChange('destinataire', e.target.value)}
                          placeholder="Nom et titre du destinataire du rapport"
                          rows={3}
                          className="w-full"
                        />
                      </div>

                      {/* Signature Électronique */}
                      <div className="space-y-2">
                        <Label htmlFor="signature-upload" className="flex items-center">
                          <PenTool className="h-4 w-4 mr-2 text-purple-600" />
                          Signature Électronique
                        </Label>
                        <div className="space-y-2">
                          <Input
                            id="signature-upload"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileUpload('signatureElectrique', e)}
                            className="w-full"
                          />
                          {rapportSupport.signatureElectrique && (
                            <div className="mt-2">
                              <img
                                src={rapportSupport.signatureElectrique}
                                alt="Signature preview"
                                className="max-w-full h-20 object-contain border rounded"
                                onError={(e) => {
                                  console.error('Error loading signature preview');
                                  e.target.style.display = 'none';
                                }}
                              />
                            </div>
                          )}
                          <div className="text-xs text-gray-400 mt-1">
                            Status: {rapportSupport.signatureElectrique ? 'Signature chargée' : 'Aucune signature'}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Report Content */}
              {loadingReport ? (
                <div className="flex items-center justify-center h-64">
                  <div className="flex flex-col items-center space-y-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p className="text-muted-foreground">Chargement du rapport...</p>
                  </div>
                </div>
              ) : reportError ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Erreur de chargement</h3>
                    <p className="text-muted-foreground mb-4">{reportError}</p>
                    <Button onClick={fetchReportData}>
                      Réessayer
                    </Button>
                  </div>
                </div>
              ) : reportData && reportData.length > 0 ? (
                <div className="space-y-6">
                  {/* Rapport Support Information */}
                  <Card key={`rapport-display-${rapportSupport.logo ? 'with-logo' : 'no-logo'}-${rapportSupport.signatureElectrique ? 'with-sig' : 'no-sig'}`}>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <FileText className="h-5 w-5 mr-2 text-[#F62D51]" />
                        Configuration du Rapport Actuelle
                        <div className="ml-auto text-sm text-gray-500">
                          {loadingRapportSupport ? 'Chargement...' :
                           (rapportSupport.logo || rapportSupport.destinataire || rapportSupport.signatureElectrique) ?
                           'Configuré' : 'Non configuré'}
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {loadingRapportSupport ? (
                        <div className="flex items-center justify-center h-32">
                          <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          <span className="ml-2">Chargement de la configuration...</span>
                        </div>
                      ) : (rapportSupport.logo || rapportSupport.destinataire || rapportSupport.signatureElectrique) ? (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {rapportSupport.logo && (
                            <div className="space-y-1">
                              <Label className="font-medium flex items-center">
                                <Image className="h-4 w-4 mr-1 text-blue-600" />
                                Logo
                              </Label>
                              <div className="border rounded p-2 bg-gray-50">
                                <img
                                  src={rapportSupport.logo}
                                  alt="Logo"
                                  className="max-w-full h-16 object-contain mx-auto"
                                  onError={(e) => {
                                    console.error('Error loading logo image');
                                    e.target.style.display = 'none';
                                  }}
                                />
                              </div>
                            </div>
                          )}
                          {rapportSupport.destinataire && (
                            <div className="space-y-1">
                              <Label className="font-medium flex items-center">
                                <User className="h-4 w-4 mr-1 text-green-600" />
                                Destinataire
                              </Label>
                              <p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded border">
                                {rapportSupport.destinataire}
                              </p>
                            </div>
                          )}
                          {rapportSupport.signatureElectrique && (
                            <div className="space-y-1">
                              <Label className="font-medium flex items-center">
                                <PenTool className="h-4 w-4 mr-1 text-purple-600" />
                                Signature Électronique
                              </Label>
                              <div className="border rounded p-2 bg-gray-50">
                                <img
                                  src={rapportSupport.signatureElectrique}
                                  alt="Signature"
                                  className="max-w-full h-16 object-contain mx-auto"
                                  onError={(e) => {
                                    console.error('Error loading signature image');
                                    e.target.style.display = 'none';
                                  }}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <FileText className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                          <p>Aucune configuration de rapport trouvée</p>
                          <p className="text-sm">Utilisez la section ci-dessous pour configurer le logo, destinataire et signature</p>
                          {/* Debug info */}
                          <div className="mt-4 text-xs text-gray-400 bg-gray-100 p-2 rounded">
                            Debug: Logo={rapportSupport.logo ? 'Present' : 'Empty'},
                            Destinataire={rapportSupport.destinataire || 'Empty'},
                            Signature={rapportSupport.signatureElectrique ? 'Present' : 'Empty'}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Report Header */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-xl">Rapport de Mission d'Audit</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* 1. Diffusion */}
                      <div>
                        <h3 className="text-lg font-semibold mb-3">1. Diffusion</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-1">
                            <Label className="font-medium">Chef de mission</Label>
                            <p className="text-sm text-muted-foreground">{reportData[0]?.chefmission || 'N/A'}</p>
                          </div>
                          <div className="space-y-1">
                            <Label className="font-medium">Directeur d'audit</Label>
                            <p className="text-sm text-muted-foreground">{reportData[0]?.directeuraudit || 'N/A'}</p>
                          </div>
                          <div className="space-y-1">
                            <Label className="font-medium">Autres participants</Label>
                            <p className="text-sm text-muted-foreground">
                              {[...new Set(
                                reportData
                                  .flatMap(row => [row.activity_responsable, row.constat_responsable])
                                  .filter(id => id !== null && id !== undefined)
                              )].join(', ') || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* 2. Résumé */}
                      <div>
                        <h3 className="text-lg font-semibold mb-3">2. Résumé</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="space-y-1">
                            <Label className="font-medium">Mission d'audit</Label>
                            <p className="text-sm text-muted-foreground">{reportData[0]?.mission_name || 'N/A'}</p>
                          </div>
                          <div className="space-y-1">
                            <Label className="font-medium">Catégorie</Label>
                            <p className="text-sm text-muted-foreground">{reportData[0]?.categorie || 'N/A'}</p>
                          </div>
                          <div className="space-y-1">
                            <Label className="font-medium">Évaluation</Label>
                            <p className="text-sm text-muted-foreground">{reportData[0]?.evaluation || 'N/A'}</p>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium mb-2">2.1 Objectif de la mission d'audit</h4>
                            <p className="text-sm text-muted-foreground bg-gray-50 p-3 rounded-md">
                              {reportData[0]?.objectif || 'N/A'}
                            </p>
                          </div>

                          <div>
                            <h4 className="font-medium mb-2">2.2 Points forts</h4>
                            <p className="text-sm text-muted-foreground bg-green-50 p-3 rounded-md">
                              {reportData[0]?.pointfort || 'N/A'}
                            </p>
                          </div>

                          <div>
                            <h4 className="font-medium mb-2">2.3 Points faibles</h4>
                            <p className="text-sm text-muted-foreground bg-red-50 p-3 rounded-md">
                              {reportData[0]?.pointfaible || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 3. Contexte */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">3. Contexte</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* 3.1 Managers opérationnels audités */}
                      <div>
                        <h4 className="font-medium mb-3">3.1 Managers opérationnels audités</h4>
                        <div className="border rounded-md overflow-hidden">
                          <table className="w-full">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-2 text-left font-medium">Élément audité</th>
                                <th className="px-4 py-2 text-left font-medium">Propriétaire</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td className="px-4 py-2 text-sm">
                                  {[
                                    reportData[0]?.risk_names,
                                    reportData[0]?.entity_names,
                                    reportData[0]?.control_names,
                                    reportData[0]?.incident_names,
                                    reportData[0]?.organizational_process_names,
                                  ].filter(name => name).join(', ') || 'N/A'}
                                </td>
                                <td className="px-4 py-2 text-sm">{reportData[0]?.recommendation_responsable || 'N/A'}</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* 3.2 Coûts de la mission d'audit */}
                      <div>
                        <h4 className="font-medium mb-3">3.2 Coûts de la mission d'audit</h4>
                        <div className="border rounded-md overflow-hidden">
                          <table className="w-full">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-2 text-left font-medium">Description</th>
                                <th className="px-4 py-2 text-left font-medium">Valeur</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td className="px-4 py-2 text-sm">Charge de travail estimée (Heures)</td>
                                <td className="px-4 py-2 text-sm">{reportData[0]?.chargedetravailestimee || 'N/A'}</td>
                              </tr>
                              <tr className="bg-gray-50">
                                <td className="px-4 py-2 text-sm">Charge de travail effective (Heures)</td>
                                <td className="px-4 py-2 text-sm">{reportData[0]?.chargedetravaileffective || 'N/A'}</td>
                              </tr>
                              <tr>
                                <td className="px-4 py-2 text-sm">Total des dépenses</td>
                                <td className="px-4 py-2 text-sm">{reportData[0]?.depenses || 'N/A'}</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 4. Constats */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">4. Constats et Recommandations</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {/* 4.1 Constats urgents */}
                      <div className="mb-6">
                        <h4 className="font-medium mb-3">4.1 Constats urgents</h4>
                        <div className="border rounded-md overflow-hidden">
                          <table className="w-full">
                            <thead className="bg-red-50">
                              <tr>
                                <th className="px-4 py-2 text-left font-medium">Constat</th>
                                <th className="px-4 py-2 text-left font-medium">Impact</th>
                              </tr>
                            </thead>
                            <tbody>
                              {reportData.filter(row => row.constat_impact === "très fort").length > 0 ? (
                                reportData
                                  .filter(row => row.constat_impact === "très fort")
                                  .map((row, index) => (
                                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-red-25'}>
                                      <td className="px-4 py-2 text-sm">{row.constat_name || 'N/A'}</td>
                                      <td className="px-4 py-2 text-sm">
                                        <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                                          {row.constat_impact || 'N/A'}
                                        </span>
                                      </td>
                                    </tr>
                                  ))
                              ) : (
                                <tr>
                                  <td colSpan="2" className="px-4 py-2 text-sm text-center text-muted-foreground">
                                    Aucun constat urgent trouvé.
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* 4.2 Détails des constats et recommandations */}
                      <div>
                        <h4 className="font-medium mb-3">4.2 Détails des constats et recommandations</h4>
                        <div className="border rounded-md overflow-hidden">
                          <div className="overflow-x-auto">
                            <table className="w-full min-w-[800px]">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-3 py-2 text-left font-medium text-xs">Constat</th>
                                  <th className="px-3 py-2 text-left font-medium text-xs">Impact</th>
                                  <th className="px-3 py-2 text-left font-medium text-xs">Recommandation</th>
                                  <th className="px-3 py-2 text-left font-medium text-xs">Détails</th>
                                  <th className="px-3 py-2 text-left font-medium text-xs">Propriétaire</th>
                                  <th className="px-3 py-2 text-left font-medium text-xs">Date de fin</th>
                                </tr>
                              </thead>
                              <tbody>
                                {reportData.filter(row => row.constat_name).length > 0 ? (
                                  reportData
                                    .filter(row => row.constat_name)
                                    .map((row, index) => (
                                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                        <td className="px-3 py-2 text-xs">{row.constat_name || 'N/A'}</td>
                                        <td className="px-3 py-2 text-xs">
                                          <span className={`px-2 py-1 rounded-full text-xs ${
                                            row.constat_impact === 'très fort' ? 'bg-red-100 text-red-800' :
                                            row.constat_impact === 'fort' ? 'bg-orange-100 text-orange-800' :
                                            row.constat_impact === 'moyen' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-green-100 text-green-800'
                                          }`}>
                                            {row.constat_impact || 'N/A'}
                                          </span>
                                        </td>
                                        <td className="px-3 py-2 text-xs">{row.recommendation_name || 'N/A'}</td>
                                        <td className="px-3 py-2 text-xs max-w-[200px] truncate" title={row.recommendation_details}>
                                          {row.recommendation_details || 'N/A'}
                                        </td>
                                        <td className="px-3 py-2 text-xs">{row.recommendation_responsable || 'N/A'}</td>
                                        <td className="px-3 py-2 text-xs">{row.datefin || 'N/A'}</td>
                                      </tr>
                                    ))
                                ) : (
                                  <tr>
                                    <td colSpan="6" className="px-4 py-2 text-sm text-center text-muted-foreground">
                                      Aucun constat trouvé.
                                    </td>
                                  </tr>
                                )}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h4 className="text-lg font-semibold mb-2">Aucune donnée de rapport</h4>
                    <p className="text-muted-foreground mb-4">
                      Aucune donnée de rapport n'est disponible pour cette mission d'audit.
                    </p>
                    <Button onClick={fetchReportData}>
                      Actualiser
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Email Modal */}
      <EmailReportModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        defaultSubject={`Rapport d'audit - ${missionAudit?.name || ''}`}
        defaultMessage={`Bonjour,\n\nVeuillez trouver ci-joint le rapport d'audit de la mission "${missionAudit?.name || ''}".\n\nCordialement,\nL'équipe Audit.`}
        defaultAttachment={emailAttachment}
        reportTitle={missionAudit?.name || ''}
      />

      {/* PDF Preview Dialog */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-3xl w-full">
          <DialogHeader>
            <DialogTitle>Prévisualisation du Rapport PDF</DialogTitle>
          </DialogHeader>
          <div className="relative min-h-[600px] flex flex-col items-center justify-center">
            {/* Read Aloud Controls */}
            <div className="flex items-center space-x-2 mb-4">
              <Button variant="outline" onClick={handleReadAloud} disabled={ttsLoading || isSpeaking}>
                <Volume2 className="h-4 w-4 mr-2" />
                Lire à voix haute
              </Button>
              {isSpeaking && !isPaused && (
                <Button variant="ghost" onClick={handlePauseTTS}><Pause className="h-4 w-4" /></Button>
              )}
              {isSpeaking && isPaused && (
                <Button variant="ghost" onClick={handleResumeTTS}><Play className="h-4 w-4" /></Button>
              )}
              {isSpeaking && (
                <Button variant="ghost" onClick={handleStopTTS}><Square className="h-4 w-4" /></Button>
              )}
              {ttsLoading && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
            </div>
            {/* Gemini Conversation Controls */}
            <div className="flex items-center space-x-2 mb-4">
              <Button variant="outline" onClick={handleGenerateConversation} disabled={convLoading || isConvPlaying}>
                <MessageCircle className="h-4 w-4 mr-2" />
                Générer une conversation
              </Button>
              {convLoading && <Loader2 className="h-4 w-4 animate-spin ml-2" />}
              {conversation.length > 0 && !convLoading && (
                <Button variant="outline" onClick={handlePlayPauseConv}>
                  {isConvPlaying && !isConvPaused ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                  {isConvPlaying && !isConvPaused ? 'Pause' : 'Play'}
                </Button>
              )}
              {isConvPlaying && (
                <Button variant="ghost" onClick={handleStopConvTTS} className="ml-2"><Square className="h-4 w-4 mr-2" />Arrêter la lecture</Button>
              )}
            </div>
            {convError && <div className="text-destructive mb-2">{convError}</div>}
            {/* PDF Preview */}
            {pdfLoading && !pdfError && (
              <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Chargement du PDF...</span>
              </div>
            )}
            {pdfError && (
              <div className="flex flex-col items-center justify-center h-full w-full">
                <AlertTriangle className="h-8 w-8 text-destructive mb-2" />
                <p className="text-destructive mb-2">{pdfError}</p>
                <Button onClick={handleOpenPreview}>Réessayer</Button>
              </div>
            )}
            <iframe
              title="Prévisualisation PDF"
              src={`${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}/preview-pdf?token=${localStorage.getItem('authToken')}`}
              className="w-full min-h-[600px] border rounded"
              allow="fullscreen"
              allowFullScreen
              style={{ display: pdfError ? 'none' : 'block' }}
              onLoad={handlePdfLoad}
              onError={handlePdfError}
            />
          </div>
          <DialogClose asChild>
            <Button variant="outline" className="absolute top-4 right-4">Fermer</Button>
          </DialogClose>
        </DialogContent>
      </Dialog>

    </div>
  );
}

export default ConclusionTab;
